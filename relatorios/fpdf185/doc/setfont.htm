<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetFont</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetFont</h1>
<code>SetFont(<b>string</b> family [, <b>string</b> style [, <b>float</b> size]])</code>
<h2>Description</h2>
Sets the font used to print character strings. It is mandatory to call this method
at least once before printing text or the resulting document would not be valid.
<br>
The font can be either a standard one or a font added via the AddFont() method. Standard fonts
use the Windows encoding cp1252 (Western Europe).
<br>
The method can be called before the first page is created and the font is kept from page
to page.
<br>
If you just wish to change the current font size, it is simpler to call SetFontSize().
<br>
<br>
<strong>Note:</strong> the font definition files must be accessible. They are searched successively in:
<ul>
<li>The directory defined by the <code>FPDF_FONTPATH</code> constant (if that constant is defined)</li>
<li>The <code>font</code> directory located in the same directory as <code>fpdf.php</code> (if it exists)</li>
</ul>
Example using <code>FPDF_FONTPATH</code>:
<div class="doc-source">
<pre><code>define('FPDF_FONTPATH', '/home/<USER>/font');
require('fpdf.php');</code></pre>
</div>
If the file corresponding to the requested font is not found, the error "Could not include font
definition file" is raised.
<h2>Parameters</h2>
<dl class="param">
<dt><code>family</code></dt>
<dd>
Family font. It can be either a name defined by AddFont() or one of the standard families (case
insensitive):
<ul>
<li><code>Courier</code> (fixed-width)</li>
<li><code>Helvetica</code> or <code>Arial</code> (synonymous; sans serif)</li>
<li><code>Times</code> (serif)</li>
<li><code>Symbol</code> (symbolic)</li>
<li><code>ZapfDingbats</code> (symbolic)</li>
</ul>
It is also possible to pass an empty string. In that case, the current family is kept.
</dd>
<dt><code>style</code></dt>
<dd>
Font style. Possible values are (case insensitive):
<ul>
<li>empty string: regular</li>
<li><code>B</code>: bold</li>
<li><code>I</code>: italic</li>
<li><code>U</code>: underline</li>
</ul>
or any combination. The default value is regular.
Bold and italic styles do not apply to <code>Symbol</code> and <code>ZapfDingbats</code>.
</dd>
<dt><code>size</code></dt>
<dd>
Font size in points.
<br>
The default value is the current size. If no size has been specified since the beginning of
the document, the value is 12.
</dd>
</dl>
<h2>Example</h2>
<div class="doc-source">
<pre><code>// Times regular 12
$pdf-&gt;SetFont('Times');
// Arial bold 14
$pdf-&gt;SetFont('Arial', 'B', 14);
// Removes bold
$pdf-&gt;SetFont('');
// Times bold, italic and underlined 14
$pdf-&gt;SetFont('Times', 'BIU');</code></pre>
</div>
<h2>See also</h2>
<a href="addfont.htm">AddFont</a>,
<a href="setfontsize.htm">SetFontSize</a>,
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="write.htm">Write</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>

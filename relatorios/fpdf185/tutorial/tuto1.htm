<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Minimal example</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Minimal example</h1>
Let's start with the classic example:
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

</span>$pdf <span class="kw">= new </span>FPDF<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span>16<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Hello World!'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto1.php' target='_blank' class='demo'>[Demo]</a></p>
After including the library file, we create an FPDF object.
The <a href='../doc/__construct.htm'>constructor</a> is used here with the default values: pages are in A4 portrait and
the unit of measure is millimeter. It could have been specified explicitly with:
<div class="source">
<pre><code>$pdf <span class="kw">= new </span>FPDF<span class="kw">(</span><span class="str">'P'</span><span class="kw">,</span><span class="str">'mm'</span><span class="kw">,</span><span class="str">'A4'</span><span class="kw">);
</span></code></pre>
</div>
It's possible to use landscape (<code>L</code>), other page sizes (such as <code>Letter</code> and
<code>Legal</code>) and units (<code>pt</code>, <code>cm</code>, <code>in</code>).
<br>
<br>
There's no page at the moment, so we have to add one with <a href='../doc/addpage.htm'>AddPage()</a>. The origin
is at the upper-left corner and the current position is by default set at 1 cm from the
borders; the margins can be changed with <a href='../doc/setmargins.htm'>SetMargins()</a>.
<br>
<br>
Before we can print text, it's mandatory to select a font with <a href='../doc/setfont.htm'>SetFont()</a>.
We choose Arial bold 16:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span>16<span class="kw">);
</span></code></pre>
</div>
We could have specified italics with I, underlined with U or a regular font with an empty string
(or any combination). Note that the font size is given in points, not millimeters (or another user
unit); it's the only exception. The other standard fonts are Times, Courier, Symbol and ZapfDingbats.
<br>
<br>
We can now print a cell with <a href='../doc/cell.htm'>Cell()</a>. A cell is a rectangular area, possibly framed,
which contains a line of text. It is output at the current position. We specify its dimensions,
its text (centered or aligned), if borders should be drawn, and where the current position
moves after it (to the right, below or to the beginning of the next line). To add a frame, we would do this:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Hello World !'</span><span class="kw">,</span>1<span class="kw">);
</span></code></pre>
</div>
To add a new cell next to it with centered text and go to the next line, we would do:
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>Cell<span class="kw">(</span>60<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Powered by FPDF.'</span><span class="kw">,</span>0<span class="kw">,</span>1<span class="kw">,</span><span class="str">'C'</span><span class="kw">);
</span></code></pre>
</div>
Remark: the line break can also be done with <a href='../doc/ln.htm'>Ln()</a>. This method additionnaly allows to specify
the height of the break.
<br>
<br>
Finally, the document is closed and sent to the browser with <a href='../doc/output.htm'>Output()</a>. We could have saved
it to a file by passing the appropriate parameters.
<br>
<br>
<strong>Caution:</strong> in case when the PDF is sent to the browser, nothing else must be output by the
script, neither before nor after (no HTML, not even a space or a carriage return). If you send something
before, you will get the error message: "Some data has already been output, can't send PDF file". If you
send something after, the document might not display.
</body>
</html>

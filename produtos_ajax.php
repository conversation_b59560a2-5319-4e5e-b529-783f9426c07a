<?php
//at
include "conexao.php";
include "produtos_class.php";
include "produtos_ajax_sincronizacao.php"; // Incluir arquivo de sincronização
$request = "";
$pagina = 0;
if (isset($_POST['request'])) {
    $request = $_POST['request'];
    $pagina = $_POST['pagina'];
    $razao_social = $_POST['razao_social'];
    $desc_pesquisa = $_POST['desc_pesquisa'];
}
if ($request == 'carregar_grupo') {
    // Verificar se há filtro por categoria
    $categoria_filtro = isset($_POST['categoria_filtro']) ? $_POST['categoria_filtro'] : '';

    if (!empty($categoria_filtro)) {
        $query = "select distinct grupo from produtos_ib where categoria = '" . pg_escape_string($conexao, $categoria_filtro) . "' order by grupo";
    } else {
        $query = "select distinct grupo from produtos_ib order by grupo";
    }

    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $grupo = $row['grupo'];
        $response[] = array(
            "grupo" => $grupo
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'carregar_subgrupo') {
    $query = "select distinct subgrupo from produtos_ib order by subgrupo";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $subgrupo = $row['subgrupo'];
        $response[] = array(
            "subgrupo" => $subgrupo
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'buscar_categorias_produto') {
    $codigo_interno = $_POST['codigo_interno'];
    $query = "SELECT categoria, grupo FROM produtos_ib WHERE codigo_interno = " . intval($codigo_interno);
    $result = pg_query($conexao, $query);

    $response = array(
        'categoria' => '',
        'grupo' => ''
    );

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        $response = array(
            'categoria' => $row['categoria'] ?? '',
            'grupo' => $row['grupo'] ?? ''
        );
    }

    echo json_encode($response);
    die();
}
if ($request == 'carregar_categoria') {
    $query = "select distinct categoria from produtos_ib order by categoria";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $categoria = $row['categoria'];
        $response[] = array(
            "categoria" => $categoria
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'adicionarFornecedor') {
    $query = "insert into fornecedores (codigo,razao_social,fantasia,cep) values(nextval('fornecedores_codigo_seq'),upper('" . $razao_social . "'),upper('" . $razao_social . "'),'83005410')";
    $result = pg_query($conexao, $query);
    $response = "ok";
    echo json_encode($response);
    die();
}
if ($request == 'carregar_unidade') {
    $query = "select distinct unidade from produtos_ib order by unidade";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $unidade = $row['unidade'];
        $response[] = array(
            "unidade" => $unidade
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'carregar_fornecedor') {
    $query = "select codigo,razao_social from fornecedores order by razao_social";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $razao_social = $row['razao_social'];
        $codigo = $row['codigo'];
        $response[] = array(
            "codigo" => $codigo,
            "razao_social" => $razao_social
        );
    }
    echo json_encode($response);
    die();
}
// Fetch all records
if ($request == 'fetchall') {
    // Garantir que $pagina seja um número inteiro
    $pagina = intval($pagina);

    // Log para depuração
    error_log("Requisição: fetchall, Página: $pagina, Termo: $desc_pesquisa");

    // Receber filtros se enviados
    $filtros = isset($_POST['filtros']) ? json_decode($_POST['filtros'], true) : array();

    // Construir condições WHERE baseadas nos filtros
    $condicoes_where = array();
    $joins_adicionais = "";

    // Filtros de e-commerce baseados no status
    $status_filtros = array();

    if (!empty($filtros['nuvemshop']) || !empty($filtros['nuvem_normal']) || !empty($filtros['nuvem_vitrine']) || !empty($filtros['nuvem_variante'])) {
        if (!empty($filtros['nuvem_normal'])) {
            $status_filtros[] = "'ENS'";
        }
        if (!empty($filtros['nuvem_vitrine'])) {
            $status_filtros[] = "'ENSVI'";
        }
        if (!empty($filtros['nuvem_variante'])) {
            $status_filtros[] = "'ENSV'";
        }
        // Se apenas Nuvemshop geral foi selecionado, incluir todos os status da Nuvemshop
        if (!empty($filtros['nuvemshop']) && empty($filtros['nuvem_normal']) && empty($filtros['nuvem_vitrine']) && empty($filtros['nuvem_variante'])) {
            $status_filtros[] = "'ENS'";
            $status_filtros[] = "'ENSVI'";
            $status_filtros[] = "'ENSV'";
            $status_filtros[] = "'E'"; // Status antigo
        }
    }

    // Filtro para produtos apenas locais
    if (!empty($filtros['apenas_locais'])) {
        $condicoes_where[] = "(p.status IS NULL OR p.status = '' OR p.status NOT IN ('ENS', 'ENSVI', 'ENSV', 'E'))";
    }

    // Se há filtros de status específicos, adicionar à condição
    if (!empty($status_filtros)) {
        $condicoes_where[] = "p.status IN (" . implode(',', $status_filtros) . ")";
    }

    // Filtros de categoria e grupo
    if (!empty($filtros['categoria']) || !empty($filtros['grupo'])) {
        $joins_adicionais = " INNER JOIN produtos_ib pib ON p.codigo_interno = pib.codigo_interno ";

        if (!empty($filtros['categoria'])) {
            $condicoes_where[] = "pib.categoria = '" . pg_escape_string($conexao, $filtros['categoria']) . "'";
        }

        if (!empty($filtros['grupo'])) {
            $condicoes_where[] = "pib.grupo = '" . pg_escape_string($conexao, $filtros['grupo']) . "'";
        }
    }

    // Construir query base
    $base_query = "FROM produtos p" . $joins_adicionais;

    // Adicionar condições de pesquisa
    if (is_numeric($desc_pesquisa)) {
        $condicoes_where[] = "p.codigo_gtin='" . $desc_pesquisa . "'";
    } else if (!empty($desc_pesquisa)) {
        $condicoes_where[] = "p.descricao LIKE upper('%" . $desc_pesquisa . "%')";
    }

    // Montar WHERE final
    $where_clause = "";
    if (!empty($condicoes_where)) {
        $where_clause = " WHERE " . implode(' AND ', $condicoes_where);
    }

    // Queries finais
    $query = "SELECT p.codigo_gtin, p.descricao, p.codigo_interno, p.status " . $base_query . $where_clause . " ORDER BY p.descricao ASC LIMIT 50 OFFSET " . $pagina;
    $query_quantos = "SELECT count(*) " . $base_query . $where_clause;

    // Log para depuração
    error_log("Query: $query");

    $result = pg_query($conexao, $query);
    $result_quantos = pg_query($conexao, $query_quantos);
    $row_quantos = pg_fetch_row($result_quantos);
    $response = array();

    while ($row = pg_fetch_assoc($result)) {
        $codigo_gtin = $row['codigo_gtin'];
        $descricao = $row['descricao'];
        $codigo_interno = $row['codigo_interno'];
        $status = $row['status'];
        $quantos = $row_quantos[0];

        $response[] = array(
            "codigo_gtin" => $codigo_gtin,
            "descricao" => $descricao,
            "codigo_interno" => $codigo_interno,
            "status" => $status,
            "quantos" => $quantos,
            "pagina" => $pagina // Adicionar o valor de pagina para a paginação
        );
    }
    echo json_encode($response);
    die();
}
//PARA CONSULTAR CODIGO DO PRODUTO EXISTENTE
if ($request == 'consultarCodigoProduto') {
    $response = "0";
    $codigo_gtin= $_POST['codigo_gtin'];
    $query="select codigo_interno from produtos where codigo_gtin='".$codigo_gtin."'";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $codigo_interno = $row['codigo_interno'];
        $response[] = array(
            "codigo_interno" => $codigo_interno
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'consultarCodigoProdutoGrade') {
    $response = "0";
    $codigo_gtin= $_POST['codigo_gtin'];
    $query="select descricao from produtos where codigo_gtin='".$codigo_gtin."'";
    $result = pg_query($conexao, $query);
    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $descricao = $row['descricao'];
        $response[] = array(
            "descricao" => $descricao
        );
    }
    echo json_encode($response);
    die();
}
// PARA ALTERAR PRODUTOS
if ($request == 'atribuirProdutos') {
    $produtos = new produtos();
    $produtos->codigo_interno = $_POST['codigo_interno'];

    // Verificar se o produto existe
    $check_query = "SELECT codigo_interno FROM produtos WHERE codigo_interno = " . $produtos->codigo_interno;
    $check_result = pg_query($conexao, $check_query);

    if (!$check_result || pg_num_rows($check_result) == 0) {
        // Produto não encontrado
        $response = array(
            "success" => false,
            "error" => "Produto não encontrado com o código " . $produtos->codigo_interno
        );
        echo json_encode($response);
        die();
    }

    // Produto existe, buscar detalhes
    $query = "select p.codigo_interno, p.codigo_gtin, p.descricao, pb.descricao_detalhada,pb.grupo,pb.subgrupo,pb.categoria, pb.preco_venda, pb.preco_compra, pb.perc_lucro,pb.codigo_ncm,pb.cest,".
    "pb.cfop, pt.aliquota_icms,pb.produto_balanca,pb.validade, po.dt_cadastro, po.dt_ultima_alteracao,p.status,pb.unidade,po.codfor,pt.situacao_tributaria, ".
    "po.perc_desc_a, po.val_desc_a, po.perc_desc_b, po.val_desc_b,po.perc_desc_c,po.val_desc_c,po.perc_desc_d,po.val_desc_d, ".
    "po.perc_desc_e,po.val_desc_e,pt.aliquota_calculo_credito,pt.perc_dif,pt.modalidade_deter_bc_icms,pt.icms_reducao_bc, ".
    "pt.modalidade_deter_bc_icms_st, po.tamanho,po.vencimento, pt.aliquota_fcp_st, po.descricao_personalizada,po.preco_gelado, ".
    " po.desc_etiqueta, po.inativo,po.producao, pt.aliquota_fcp, po.qtde, po.qtde_min, pt.icms_reducao_bc_st, pt.perc_mva_icms_st,pt.aliquota_icms_st, ".
    " pt.ipi_reducao_bc, pt.aliquota_ipi, pt.ipi_reducao_bc_st,pt.aliquota_ipi_st, pt.cst_ipi, pt.calculo_ipi, ".
    " pt.pis_reducao_bc, pt.aliquita_pis, pt.pis_reducao_bc_st,pt.aliquota_pis_st, pt.cst_pis, pt.calculo_pis, ".
    " pt.cofins_reducao_bc, pt.aliquota_cofins, pt.cofins_reducao_bc_st,pt.aliquota_cofins_st, pt.cst_cofins, pt.calculo_cofins,po.comprimento,po.largura,po.altura,po.peso ".
        " from produtos p inner join produtos_ib pb on p.codigo_interno = pb.codigo_interno ".
        " inner join produtos_ou po on p.codigo_interno=po.codigo_interno ".
        " inner join produtos_tb pt on p.codigo_interno=pt.codigo_interno where p.codigo_interno=" . $produtos->codigo_interno;

    $result = pg_query($conexao, $query);

    if (!$result) {
        // Erro na consulta
        $error = pg_last_error($conexao);
        $response = array(
            "success" => false,
            "error" => "Erro ao buscar detalhes do produto: " . $error
        );
        echo json_encode($response);
        die();
    }

    $response = array();

    if (pg_num_rows($result) == 0) {
        // Produto existe mas não tem detalhes completos
        $response = array(
            "success" => false,
            "error" => "Produto encontrado, mas não tem detalhes completos. Pode ser necessário recriar o produto."
        );
        echo json_encode($response);
        die();
    }

    // Produto encontrado com sucesso
    while ($row = pg_fetch_assoc($result)) {
        $response[] = array(
            "codigo_interno" => $row['codigo_interno'],
            "codigo_gtin" => $row['codigo_gtin'],
            "descricao" => $row['descricao'],
            "descricao_detalhada" => $row['descricao_detalhada'],
            "grupo" => $row['grupo'],
            "subgrupo" => $row['subgrupo'],
            "categoria" => $row['categoria'],
            "preco_venda" => number_format($row['preco_venda'],2,',',''),
            "preco_compra" => number_format($row['preco_compra'],2,',',''),
            "perc_lucro" => number_format($row['perc_lucro'],2,',',''),
            "codigo_ncm" => $row['codigo_ncm'],
            "cest" => $row['cest'],
            "cfop" => $row['cfop'],
            "aliquota_icms" => number_format($row['aliquota_icms'],2,',',''),
            "produto_balanca" => $row['produto_balanca'],
            "producao" => $row['producao'],
            "validade" => $row['validade'],
            "dt_cadastro" => date("d/m/Y",strtotime($row['dt_cadastro'])),
            "dt_ultima_alteracao" => date("d/m/Y",strtotime($row['dt_ultima_alteracao'])),
            "unidade" => $row['unidade'],
            "status" => $row['status'],
            "codfor" => $row['codfor'],
            "situacao_tributaria" => $row['situacao_tributaria'],
            "perc_desc_a" => number_format($row['perc_desc_a'],2,',',''),
            "val_desc_a" => number_format($row['val_desc_a'],2,',',''),
            "perc_desc_b" => number_format($row['perc_desc_b'],2,',',''),
            "val_desc_b" => number_format($row['val_desc_b'],2,',',''),
            "perc_desc_c" => number_format($row['perc_desc_c'],2,',',''),
            "val_desc_c" => number_format($row['val_desc_c'],2,',',''),
            "perc_desc_d" => number_format($row['perc_desc_d'],2,',',''),
            "val_desc_d" => number_format($row['val_desc_d'],2,',',''),
            "perc_desc_e" => number_format($row['perc_desc_e'],2,',',''),
            "val_desc_e" => number_format($row['val_desc_e'],2,',',''),
            "aliquota_calculo_credito" => number_format($row['aliquota_calculo_credito'],2,',',''),
            "perc_dif" => number_format($row['perc_dif'],2,',',''),
            "mod_deter_bc_icms" => $row['modalidade_deter_bc_icms'],
            "perc_redu_icms" => number_format($row['icms_reducao_bc'],2,',',''),
            "mod_deter_bc_icms_st" => $row['modalidade_deter_bc_icms_st'],
            "tamanho" => $row['tamanho'],
            "vencimento" => $row['vencimento'],
            "aliq_fcp_st" => number_format($row['aliquota_fcp_st'],2,',',''),
            "descricao_personalizada" => $row['descricao_personalizada'],
            "valorGelado" => number_format($row['preco_gelado'],2,',',''),
            "prod_desc_etiqueta" => $row['desc_etiqueta'],
            "inativo" => $row['inativo'],
            "aliq_fcp" => number_format($row['aliquota_fcp'],2,',',''),
            "qtde" => number_format($row['qtde'],2,',',''),
            "qtde_min" => number_format($row['qtde_min'],2,',',''),
            "perc_redu_icms_st" => number_format($row['icms_reducao_bc_st'],2,',',''),
            "perc_mv_adic_icms_st" => number_format($row['perc_mva_icms_st'],2,',',''),
            "aliq_icms_st" => number_format($row['aliquota_icms_st'],2,',',''),
            "ipi_reducao_bc" => number_format($row['ipi_reducao_bc'],2,',',''),
            "aliquota_ipi" => number_format($row['aliquota_ipi'],2,',',''),
            "ipi_reducao_bc_st" => number_format($row['ipi_reducao_bc_st'],2,',',''),
            "aliquota_ipi_st" => number_format($row['aliquota_ipi_st'],2,',',''),
            "cst_ipi" => $row['cst_ipi'],
            "calculo_ipi" => $row['calculo_ipi'],
            "pis_reducao_bc" => number_format($row['pis_reducao_bc'],2,',',''),
            "aliquota_pis" => number_format($row['aliquita_pis'],2,',',''),
            "pis_reducao_bc_st" => number_format($row['pis_reducao_bc_st'],2,',',''),
            "aliquota_pis_st" => number_format($row['aliquota_pis_st'],2,',',''),
            "cst_pis" => $row['cst_pis'],
            "calculo_pis" => $row['calculo_pis'],
            "cofins_reducao_bc" => number_format($row['cofins_reducao_bc'],2,',',''),
            "aliquota_cofins" => number_format($row['aliquota_cofins'],2,',',''),
            "cofins_reducao_bc_st" => number_format($row['cofins_reducao_bc_st'],2,',',''),
            "aliquota_cofins_st" => number_format($row['aliquota_cofins_st'],2,',',''),
            "cst_cofins" => $row['cst_cofins'],
            "calculo_cofins" => $row['calculo_cofins'],
            "comprimento" => $row['comprimento'],
            "largura" => $row['largura'],
            "altura" => $row['altura'],
            "peso" => $row['peso']
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'arquivoExiste') {
    $arquivo1=$_POST['arquivo1'];
    $arquivo2=$_POST['arquivo2'];
    $arquivo3=$_POST['arquivo3'];
    $arquivo4=$_POST['arquivo4'];
    $arquivo5=$_POST['arquivo5'];
    $result="";
    if(file_exists($arquivo1)){
        $result="1";
    }else{
        $result="0";
    }
    if(file_exists($arquivo2)){
        $result=$result."1";
    }else{
        $result=$result."0";
    }
    if(file_exists($arquivo3)){
        $result=$result."1";
    }else{
        $result=$result."0";
    }
    if(file_exists($arquivo4)){
        $result=$result."1";
    }else{
        $result=$result."0";
    }
    if(file_exists($arquivo5)){
        $result=$result."1";
    }else{
        $result=$result."0";
    }
    echo json_encode($result);
    die();
}
// PARA INSERIR / ATUALIZAR PRODUTOS
if ($request == 'inserirAtualizarProdutos') {
    $response = "0";
    $produtos = new produtos();
    $produtos->codigo_interno = $_POST['codigo_interno'];
    $produtos->codigo_gtin = $_POST['codigo_gtin'];
    $produtos->descricao = $_POST['descricao'];
    if($produtos->codigo_gtin==0 or $produtos->codigo_gtin==''){
        die();
        return;
    }
    // Não atualizar o status do produto com base no checkbox "Vender no E-commerce"
    // O status só deve ser atualizado quando o produto é efetivamente exportado para a Nuvemshop
    // Manter o status atual do produto
    $query = "SELECT status FROM produtos WHERE codigo_interno = " . $produtos->codigo_interno;
    $result = pg_query($conexao, $query);
    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        $produtos->status = $row['status'];
    } else {
        $produtos->status = "";
    }
    if($produtos->codigo_interno==0)
    {
        // Verificar se o código GTIN já existe
        $check_query = "SELECT codigo_interno FROM produtos WHERE codigo_gtin = '" . $produtos->codigo_gtin . "'";
        $check_result = pg_query($conexao, $check_query);

        if (pg_num_rows($check_result) > 0) {
            // O código GTIN já existe, retornar erro
            $response = array(
                "success" => false,
                "error" => "Código GTIN já cadastrado. Por favor, use outro código."
            );
            echo json_encode($response);
            die();
        }

        // Se chegou aqui, o código GTIN não existe, podemos inserir
        $query = "insert into produtos (codigo_interno,descricao,codigo_gtin,status) values(nextval('produtos_seq'),upper('" . $produtos->descricao . "'),'" . $produtos->codigo_gtin . "','" . $produtos->status . "') returning codigo_interno";
        $result = pg_query($conexao, $query);

        // Verificar se a consulta foi bem-sucedida
        if (!$result) {
            $error = pg_last_error($conexao);
            $response = array(
                "success" => false,
                "error" => "Erro ao inserir produto: " . $error
            );
            echo json_encode($response);
            die();
        }

        $row = pg_fetch_assoc($result);
        if ($row && $row['codigo_interno'] > 0) {
            // ✅ CORREÇÃO: Atualizar o codigo_interno do produto com o valor gerado
            $produtos->codigo_interno = $row['codigo_interno'];

            $produtos_ib = new produtos_ib();
            $produtos_ib->codigo_interno = $row['codigo_interno'];
            $produtos_ib->descricao_detalhada = $_POST['descricao_detalhada'];
            $produtos_ib->grupo = $_POST['grupo'];
            $produtos_ib->subgrupo = $_POST['subgrupo'];
            $produtos_ib->categoria = $_POST['categoria'];
            $produtos_ib->unidade = $_POST['unidade'];
            $produtos_ib->preco_venda =str_replace(',','.',$_POST['preco_venda']);
            $produtos_ib->preco_compra =str_replace(',','.',$_POST['preco_compra']);
            $produtos_ib->perc_lucro =str_replace(',','.',$_POST['perc_lucro']);
            $produtos_ib->codigo_ncm = $_POST['ncm'];
            if ($_POST['produto_balanca'] == 'true') {
                $produtos_ib->produto_balanca = "1";
            } else {
                $produtos_ib->produto_balanca = "0";
            }
            $produtos_ib->validade = $_POST['validade'];
            $produtos_ib->cfop = $_POST['cfop'];
            $produtos_ib->cest = $_POST['cest'];
            $query = "insert into produtos_ib (codigo_interno,descricao_detalhada,grupo,subgrupo,categoria,unidade,preco_venda,preco_compra,perc_lucro,codigo_ncm,produto_balanca,validade,cfop,cest) " . " values('" . $produtos_ib->codigo_interno . "',upper('" . $produtos_ib->descricao_detalhada . "'),'" . $produtos_ib->grupo . "','" . $produtos_ib->subgrupo . "','" . $produtos_ib->categoria . "',upper('" . $produtos_ib->unidade . "'),'" . $produtos_ib->preco_venda . "','" . $produtos_ib->preco_compra . "','" . $produtos_ib->perc_lucro . "','" . $produtos_ib->codigo_ncm . "','" . $produtos_ib->produto_balanca . "','" . $produtos_ib->validade . "','" . $produtos_ib->cfop . "','" . $produtos_ib->cest . "')";
            $result = pg_query($conexao, $query);
            $teste=$query;
            $produtos_ou = new produtos_ou();
            $produtos_ou->codigo_interno = $row['codigo_interno'];
            $produtos_ou->percDescA = str_replace(',','.',$_POST['perc_desc_a']);
            $produtos_ou->percDescB = str_replace(',','.',$_POST['perc_desc_b']);
            $produtos_ou->percDescC = str_replace(',','.',$_POST['perc_desc_c']);
            $produtos_ou->percDescD = str_replace(',','.',$_POST['perc_desc_d']);
            $produtos_ou->percDescE = str_replace(',','.',$_POST['perc_desc_e']);
            $produtos_ou->valDescA = str_replace(',','.',$_POST['val_desc_a']);
            $produtos_ou->valDescB = str_replace(',','.',$_POST['val_desc_b']);
            $produtos_ou->valDescC = str_replace(',','.',$_POST['val_desc_c']);
            $produtos_ou->valDescD = str_replace(',','.',$_POST['val_desc_d']);
            $produtos_ou->valDescE = str_replace(',','.',$_POST['val_desc_e']);
            $produtos_ou->qtde = str_replace(',','.',$_POST['qtde']);
            $produtos_ou->qtde_min = str_replace(',','.',$_POST['qtde_min']);
            $produtos_ou->inativo = $_POST['inativo'];
            $produtos_ou->valor_Promo = $_POST['valor_Promo'];
            $produtos_ou->fornecedor = empty($_POST['codigo_fornecedor']) ? 'NULL' : $_POST['codigo_fornecedor'];
            $produtos_ou->tamanho = $_POST['tamanho'];
            $produtos_ou->comprimento = empty($_POST['comprimento']) ? 'NULL' : str_replace(',', '.', $_POST['comprimento']);
            $produtos_ou->largura = empty($_POST['largura']) ? 'NULL' : str_replace(',', '.', $_POST['largura']);
            $produtos_ou->altura = empty($_POST['altura']) ? 'NULL' : str_replace(',', '.', $_POST['altura']);
            $produtos_ou->peso = empty($_POST['peso']) ? 'NULL' : str_replace(',', '.', $_POST['peso']);
            $produtos_ou->vencimento = $_POST['vencimento'];
            if ($_POST['descricao_personalizada'] == 'true') {
                $produtos_ou->descricao_personalizada = "1";
            } else {
                $produtos_ou->descricao_personalizada = "0";
            }
            if ($_POST['produto_producao'] == 'true') {
                $produtos_ou->produto_producao= "1";
            } else {
                $produtos_ou->produto_producao= "0";
            }
            if ($_POST['inativo'] == 'true') {
                $produtos_ou->inativo = "1";
            } else {
                $produtos_ou->inativo = "0";
            }
            //$produtos_ou->dt_cadastro = '2022-08-08'; // $_POST['dt_cadastro'];
            //$produtos_ou->dt_ultima_alteracao = '2022-08-08'; // $_POST['dt_ultima_alteracao'];
            $produtos_ou->preco_gelado = str_replace(',','.',$_POST['valorGelado']);
            $produtos_ou->desc_etiqueta = $_POST['desc_etiqueta'];
            if (strlen($produtos_ou->vencimento) < 2) {
                $produtos_ou->vencimento = "2099-01-01";
            }
            $query = "insert into produtos_ou (codigo_interno,perc_desc_a,perc_desc_b,perc_desc_c,perc_desc_d,perc_desc_e,val_desc_a,val_desc_b,val_desc_c,val_desc_d,val_desc_e, " .
            "qtde,qtde_min,inativo,codfor,tamanho,vencimento,descricao_personalizada,dt_cadastro,preco_gelado,desc_etiqueta,producao,comprimento,largura,altura,peso) " .
            " values('" . $produtos_ou->codigo_interno . "','" . $produtos_ou->percDescA . "','" . $produtos_ou->percDescB . "','" . $produtos_ou->percDescC . "','" . $produtos_ou->percDescD . "','" . $produtos_ou->percDescE . "','" . $produtos_ou->valDescA . "','" . $produtos_ou->valDescB . "','" . $produtos_ou->valDescC . "','" . $produtos_ou->valDescD . "','" . $produtos_ou->valDescE . "','" . $produtos_ou->qtde . "','" . $produtos_ou->qtde_min . "','" . $produtos_ou->inativo . "'," . ($produtos_ou->fornecedor === 'NULL' ? 'NULL' : "'".$produtos_ou->fornecedor."'") . ",'" . $produtos_ou->tamanho . "','" . $produtos_ou->vencimento . "','" . $produtos_ou->descricao_personalizada . "',current_date ,'" . $produtos_ou->preco_gelado . "','" . $produtos_ou->desc_etiqueta . "','".$produtos_ou->produto_producao."'," . ($produtos_ou->comprimento === 'NULL' ? 'NULL' : "'".$produtos_ou->comprimento."'") . "," . ($produtos_ou->largura === 'NULL' ? 'NULL' : "'".$produtos_ou->largura."'") . "," . ($produtos_ou->altura === 'NULL' ? 'NULL' : "'".$produtos_ou->altura."'") . "," . ($produtos_ou->peso === 'NULL' ? 'NULL' : "'".$produtos_ou->peso."'") . ")";
            gravar($query);
            $result = pg_query($conexao, $query);

            // Verificar se a inserção em produtos_ou foi bem-sucedida
            if (!$result) {
                $error = pg_last_error($conexao);
                error_log("ERRO INSERT produtos_ou: " . $error);
                error_log("QUERY produtos_ou: " . $query);
                $response = array(
                    "success" => false,
                    "error" => "Erro ao inserir em produtos_ou: " . $error,
                    "query" => $query
                );
                echo json_encode($response);
                die();
            }
            $produtos_tb = new produtos_tb();
            $produtos_tb->codigo_interno = $row['codigo_interno'];
            $produtos_tb->ipi_reducao_bc = str_replace(',','.',$_POST['ipi_reducao_bc']);
            $produtos_tb->aliquota_ipi = str_replace(',','.',$_POST['aliquota_ipi']);
            $produtos_tb->ipi_reducao_bc_st = str_replace(',','.',$_POST['ipi_reducao_bc_st']);
            $produtos_tb->aliquota_ipi_st = str_replace(',','.',$_POST['aliquota_ipi_st']);
            $produtos_tb->pis_reducao_bc = str_replace(',','.',$_POST['pis_reducao_bc']);
            $produtos_tb->aliquota_pis = str_replace(',','.',$_POST['aliquota_pis']);
            $produtos_tb->pis_reducao_bc_st = str_replace(',','.',$_POST['pis_reducao_bc_st']);
            $produtos_tb->aliquota_pis_st = str_replace(',','.',$_POST['aliquota_pis_st']);
            $produtos_tb->cofins_reducao_bc = str_replace(',','.',$_POST['cofins_reducao_bc']);
            $produtos_tb->aliquota_cofins = str_replace(',','.',$_POST['aliquota_cofins']);
            $produtos_tb->cofins_reducao_bc_st = str_replace(',','.',$_POST['cofins_reducao_bc_st']);
            $produtos_tb->aliquota_cofins_st = str_replace(',','.',$_POST['aliquota_cofins_st']);
            $produtos_tb->situacao_tributaria = $_POST['situacao_tributaria'];
            $produtos_tb->origem = '0';
            $produtos_tb->aliquota_calculo_credito = str_replace(',','.',$_POST['aliquota_calculo_credito']);
            $produtos_tb->modalidade_deter_bc_icms = $_POST['mod_deter_bc_icms'];
            $produtos_tb->aliquota_icms = str_replace(',','.',$_POST['perc_icms']);
            $produtos_tb->icms_reducao_bc=str_replace(',','.', $_POST['perc_redu_icms']);
            $produtos_tb->modalidade_deter_bc_icms_st = $_POST['mod_deter_bc_icms_st'];
            $produtos_tb->icms_reducao_bc_st = str_replace(',','.',$_POST['perc_redu_icms_st']);
            $produtos_tb->perc_mva_icms_st = str_replace(',','.',$_POST['perc_mv_adic_icms_st']);
            $produtos_tb->aliquota_icms_st =str_replace(',','.',$_POST['aliq_icms_st']);
            $produtos_tb->ipi_cst = $_POST['cst_ipi'];
            $produtos_tb->calculo_ipi = $_POST['calculo_ipi'];
            $produtos_tb->cst_pis = $_POST['cst_pis'];
            $produtos_tb->calculo_pis = $_POST['calculo_pis'];
            $produtos_tb->cst_cofins = $_POST['cst_cofins'];
            $produtos_tb->calculo_cofins = $_POST['calculo_cofins'];
            $produtos_tb->aliquota_fcp = str_replace(',','.',$_POST['aliq_fcp']);
            $produtos_tb->aliquota_fcp_st = str_replace(',','.',$_POST['aliq_fcp_st']);
            $produtos_tb->perc_dif = str_replace(',','.',$_POST['perc_dif']);
            $query = "insert into produtos_tb (codigo_interno,
                    ipi_reducao_bc,
                    aliquota_ipi,
                    ipi_reducao_bc_st,
                    aliquota_ipi_st,
                    pis_reducao_bc,
                    aliquita_pis,
                    pis_reducao_bc_st,
                    aliquota_pis_st,
                    cofins_reducao_bc,
                    aliquota_cofins,
                    cofins_reducao_bc_st,
                    aliquota_cofins_st,
                    situacao_tributaria,
                    origem,
                    aliquota_calculo_credito,
                    modalidade_deter_bc_icms,
                    aliquota_icms,
                    icms_reducao_bc,
                    modalidade_deter_bc_icms_st,
                    icms_reducao_bc_st,
                    perc_mva_icms_st,
                    aliquota_icms_st,
                    cst_ipi,
                    calculo_ipi,
                    cst_pis,
                    calculo_pis,
                    cst_cofins,
                    calculo_cofins,
                    aliquota_fcp,aliquota_fcp_st,perc_dif)
             values('" . $produtos_tb->codigo_interno . "','" . $produtos_tb->ipi_reducao_bc . "','" . $produtos_tb->aliquota_ipi . "','" . $produtos_tb->ipi_reducao_bc_st . "','" . $produtos_tb->aliquota_ipi_st . "','" . $produtos_tb->pis_reducao_bc . "','" . $produtos_tb->aliquota_pis . "','" . $produtos_tb->pis_reducao_bc_st . "','" . $produtos_tb->aliquota_pis_st . "','" . $produtos_tb->cofins_reducao_bc . "','" . $produtos_tb->aliquota_cofins . "','" . $produtos_tb->cofins_reducao_bc_st . "','" . $produtos_tb->aliquota_cofins_st . "','" . $produtos_tb->situacao_tributaria . "','" . $produtos_tb->origem . "','" . $produtos_tb->aliquota_calculo_credito . "','" . $produtos_tb->modalidade_deter_bc_icms . "','" . $produtos_tb->aliquota_icms . "','" . $produtos_tb->icms_reducao_bc . "','" . $produtos_tb->modalidade_deter_bc_icms_st . "','" . $produtos_tb->icms_reducao_bc_st . "','" . $produtos_tb->perc_mva_icms_st . "','" . $produtos_tb->aliquota_icms_st . "','" . $produtos_tb->ipi_cst . "','" . $produtos_tb->calculo_ipi . "','" . $produtos_tb->cst_pis . "','" . $produtos_tb->calculo_pis . "','" . $produtos_tb->cst_cofins . "','" . $produtos_tb->calculo_cofins . "','" . $produtos_tb->aliquota_fcp . "','" . $produtos_tb->aliquota_fcp_st . "','" . $produtos_tb->perc_dif . "')";
            $result = pg_query($conexao, $query);
            $teste=$query;
        }
    }else
    {//UPDATE
        //PRODUTOS
        $query = "update produtos set descricao=upper('".$produtos->descricao. "'), status='".$produtos->status."' where codigo_interno=".$produtos->codigo_interno;
        $result = pg_query($conexao, $query);
        //PRODUTOS IB
        $produtos_ib = new produtos_ib();
        $produtos_ib->descricao_detalhada = $_POST['descricao_detalhada'];
        $produtos_ib->grupo = $_POST['grupo'];
        $produtos_ib->subgrupo = $_POST['subgrupo'];
        $produtos_ib->categoria = $_POST['categoria'];
        $produtos_ib->unidade = $_POST['unidade'];
        $produtos_ib->preco_venda =str_replace(',','.',$_POST['preco_venda']);
        $produtos_ib->preco_compra =str_replace(',','.',$_POST['preco_compra']);
        $produtos_ib->perc_lucro =str_replace(',','.',$_POST['perc_lucro']);
        $produtos_ib->codigo_ncm = $_POST['ncm'];
        if ($_POST['produto_balanca'] == 'true') {
            $produtos_ib->produto_balanca = "1";
        } else {
            $produtos_ib->produto_balanca = "0";
        }
        $produtos_ib->validade = $_POST['validade'];
        $produtos_ib->cfop = $_POST['cfop'];
        $produtos_ib->cest = $_POST['cest'];
        $query = "update produtos_ib set ".
        " descricao_detalhada=upper('".$produtos_ib->descricao_detalhada."'),".
        " grupo='".$produtos_ib->grupo."',".
        " subgrupo='".$produtos_ib->subgrupo."',".
        " categoria='".$produtos_ib->categoria."',".
        " unidade='".$produtos_ib->unidade."',".
        " preco_venda='".$produtos_ib->preco_venda."',".
        " preco_compra='".$produtos_ib->preco_compra."',".
        " perc_lucro='".$produtos_ib->perc_lucro."',".
        " codigo_ncm='".$produtos_ib->codigo_ncm."',".
        " produto_balanca='".$produtos_ib->produto_balanca."',".
        " validade='".$produtos_ib->validade."',".
        " cfop='".$produtos_ib->cfop."',".
        " cest='".$produtos_ib->cest."'".
        " where codigo_interno=".$produtos->codigo_interno;
        $result = pg_query($conexao, $query);
        //PRODUTOS OU
        $produtos_ou = new produtos_ou();
        $produtos_ou->percDescA = str_replace(',','.',$_POST['perc_desc_a']);
        $produtos_ou->percDescB = str_replace(',','.',$_POST['perc_desc_b']);
        $produtos_ou->percDescC = str_replace(',','.',$_POST['perc_desc_c']);
        $produtos_ou->percDescD = str_replace(',','.',$_POST['perc_desc_d']);
        $produtos_ou->percDescE = str_replace(',','.',$_POST['perc_desc_e']);
        $produtos_ou->valDescA = str_replace(',','.',$_POST['val_desc_a']);
        $produtos_ou->valDescB = str_replace(',','.',$_POST['val_desc_b']);
        $produtos_ou->valDescC = str_replace(',','.',$_POST['val_desc_c']);
        $produtos_ou->valDescD = str_replace(',','.',$_POST['val_desc_d']);
        $produtos_ou->valDescE = str_replace(',','.',$_POST['val_desc_e']);
        $produtos_ou->qtde = str_replace(',','.',$_POST['qtde']);
        $produtos_ou->qtde_min = str_replace(',','.',$_POST['qtde_min']);
        $produtos_ou->inativo = $_POST['inativo'];
        $produtos_ou->valor_Promo = $_POST['valor_Promo'];
        $produtos_ou->fornecedor = empty($_POST['codigo_fornecedor']) ? 'NULL' : $_POST['codigo_fornecedor'];
        $produtos_ou->tamanho = $_POST['tamanho'];
        $produtos_ou->comprimento = empty($_POST['comprimento']) ? 'NULL' : str_replace(',', '.', $_POST['comprimento']);
        $produtos_ou->largura = empty($_POST['largura']) ? 'NULL' : str_replace(',', '.', $_POST['largura']);
        $produtos_ou->altura = empty($_POST['altura']) ? 'NULL' : str_replace(',', '.', $_POST['altura']);
        $produtos_ou->peso = empty($_POST['peso']) ? 'NULL' : str_replace(',', '.', $_POST['peso']);
        $produtos_ou->vencimento = $_POST['vencimento'];
        if ($_POST['descricao_personalizada'] == 'true') {
            $produtos_ou->descricao_personalizada = "1";
        } else {
            $produtos_ou->descricao_personalizada = "0";
        }
        if ($_POST['produto_producao'] == 'true') {
            $produtos_ou->produto_producao= "1";
        } else {
            $produtos_ou->produto_producao= "0";
        }
        if ($_POST['inativo'] == 'true') {
            $produtos_ou->inativo = "1";
        } else {
            $produtos_ou->inativo = "0";
        }
        $produtos_ou->preco_gelado = str_replace(',','.',$_POST['valorGelado']);
        $produtos_ou->desc_etiqueta = $_POST['desc_etiqueta'];
        if (strlen($produtos_ou->vencimento) < 2) {
            $produtos_ou->vencimento = "2099-01-01";
        }
        $query = "update produtos_ou  set ".
            " perc_desc_a='".$produtos_ou->percDescA."',".
            " perc_desc_b='".$produtos_ou->percDescB."',".
            " perc_desc_c='".$produtos_ou->percDescC."',".
            " perc_desc_d='".$produtos_ou->percDescD."',".
            " perc_desc_e='".$produtos_ou->percDescE."',".
            " val_desc_a='".$produtos_ou->valDescA."',".
            " val_desc_b='".$produtos_ou->valDescB."',".
            " val_desc_c='".$produtos_ou->valDescC."',".
            " val_desc_d='".$produtos_ou->valDescD."',".
            " val_desc_e='".$produtos_ou->valDescE."',".
            " qtde='".$produtos_ou->qtde."',".
            " qtde_min='".$produtos_ou->qtde_min."',".
            " inativo='".$produtos_ou->inativo."',".
            " codfor=".($produtos_ou->fornecedor === 'NULL' ? 'NULL' : "'".$produtos_ou->fornecedor."'").",".
            " tamanho='".$produtos_ou->tamanho."',".
            " vencimento='".$produtos_ou->vencimento."',".
            " descricao_personalizada='".$produtos_ou->descricao_personalizada."',".
            " dt_ultima_alteracao=current_date, ".
            " preco_gelado='".$produtos_ou->preco_gelado."',".
            " desc_etiqueta='".$produtos_ou->desc_etiqueta."',".
            " producao='".$produtos_ou->produto_producao."',".
            " comprimento=".($produtos_ou->comprimento === 'NULL' ? 'NULL' : "'".$produtos_ou->comprimento."'").",".
            " largura=".($produtos_ou->largura === 'NULL' ? 'NULL' : "'".$produtos_ou->largura."'").",".
            " altura=".($produtos_ou->altura === 'NULL' ? 'NULL' : "'".$produtos_ou->altura."'").",".
            " peso=".($produtos_ou->peso === 'NULL' ? 'NULL' : "'".$produtos_ou->peso."'").
            " where codigo_interno=".$produtos->codigo_interno;
        $result = pg_query($conexao, $query);

        // Verificar se a atualização em produtos_ou foi bem-sucedida
        if (!$result) {
            $error = pg_last_error($conexao);
            error_log("ERRO UPDATE produtos_ou: " . $error);
            error_log("QUERY produtos_ou: " . $query);
            $response = array(
                "success" => false,
                "error" => "Erro ao atualizar produtos_ou: " . $error,
                "query" => $query
            );
            echo json_encode($response);
            die();
        }

        // Verificar se algum registro foi afetado
        $affected_rows = pg_affected_rows($result);
        if ($affected_rows == 0) {
            error_log("AVISO: Nenhuma linha afetada no UPDATE produtos_ou para codigo_interno: " . $produtos->codigo_interno);
        }
        //PRODUTOS_TB //back
        $produtos_tb = new produtos_tb();
        $produtos_tb->ipi_reducao_bc = str_replace(',','.',$_POST['ipi_reducao_bc']);
        $produtos_tb->aliquota_ipi = str_replace(',','.',$_POST['aliquota_ipi']);
        $produtos_tb->ipi_reducao_bc_st = str_replace(',','.',$_POST['ipi_reducao_bc_st']);
        $produtos_tb->aliquota_ipi_st = str_replace(',','.',$_POST['aliquota_ipi_st']);
        $produtos_tb->pis_reducao_bc = str_replace(',','.',$_POST['pis_reducao_bc']);
        $produtos_tb->aliquota_pis = str_replace(',','.',$_POST['aliquota_pis']);
        $produtos_tb->pis_reducao_bc_st = str_replace(',','.',$_POST['pis_reducao_bc_st']);
        $produtos_tb->aliquota_pis_st = str_replace(',','.',$_POST['aliquota_pis_st']);
        $produtos_tb->cofins_reducao_bc = str_replace(',','.',$_POST['cofins_reducao_bc']);
        $produtos_tb->aliquota_cofins = str_replace(',','.',$_POST['aliquota_cofins']);
        $produtos_tb->cofins_reducao_bc_st = str_replace(',','.',$_POST['cofins_reducao_bc_st']);
        $produtos_tb->aliquota_cofins_st = str_replace(',','.',$_POST['aliquota_cofins_st']);
        $produtos_tb->situacao_tributaria = $_POST['situacao_tributaria'];
        $produtos_tb->origem = '0';
        $produtos_tb->aliquota_calculo_credito = str_replace(',','.',$_POST['aliquota_calculo_credito']);
        $produtos_tb->modalidade_deter_bc_icms = $_POST['mod_deter_bc_icms'];
        $produtos_tb->aliquota_icms =str_replace(',','.',$_POST['perc_icms']);
        $produtos_tb->icms_reducao_bc = str_replace(',','.',$_POST['perc_redu_icms']);
        $produtos_tb->modalidade_deter_bc_icms_st = $_POST['mod_deter_bc_icms_st'];
        $produtos_tb->icms_reducao_bc_st = str_replace(',','.',$_POST['perc_redu_icms_st']);
        $produtos_tb->perc_mva_icms_st = str_replace(',','.',$_POST['perc_mv_adic_icms_st']);
        $produtos_tb->aliquota_icms_st = str_replace(',','.',$_POST['aliq_icms_st']);
        $produtos_tb->ipi_cst = $_POST['cst_ipi'];
        $produtos_tb->calculo_ipi = $_POST['calculo_ipi'];
        $produtos_tb->cst_pis = $_POST['cst_pis'];
        $produtos_tb->calculo_pis = $_POST['calculo_pis'];
        $produtos_tb->cst_cofins = $_POST['cst_cofins'];
        $produtos_tb->calculo_cofins = $_POST['calculo_cofins'];
        $produtos_tb->aliquota_fcp = str_replace(',','.',$_POST['aliq_fcp']);
        $produtos_tb->aliquota_fcp_st = str_replace(',','.',$_POST['aliq_fcp_st']);
        $produtos_tb->perc_dif = str_replace(',','.',$_POST['perc_dif']);
        $query = "update produtos_tb set ipi_reducao_bc='".$produtos_tb->ipi_reducao_bc."',".
                    "aliquota_ipi='$produtos_tb->aliquota_ipi', ipi_reducao_bc_st='$produtos_tb->ipi_reducao_bc_st',".
                    "aliquota_ipi_st='$produtos_tb->aliquota_ipi_st',pis_reducao_bc='$produtos_tb->pis_reducao_bc',".
                    "aliquita_pis='$produtos_tb->aliquota_pis',pis_reducao_bc_st='$produtos_tb->pis_reducao_bc_st',".
                    "aliquota_pis_st='$produtos_tb->aliquota_pis_st',cofins_reducao_bc='$produtos_tb->cofins_reducao_bc',".
                    "aliquota_cofins='$produtos_tb->aliquota_cofins',cofins_reducao_bc_st='$produtos_tb->cofins_reducao_bc_st',".
                    "aliquota_cofins_st='$produtos_tb->aliquota_cofins_st',situacao_tributaria='$produtos_tb->situacao_tributaria',".
                    "origem='$produtos_tb->origem',aliquota_calculo_credito='$produtos_tb->aliquota_calculo_credito',".
                    "modalidade_deter_bc_icms='$produtos_tb->modalidade_deter_bc_icms',aliquota_icms='$produtos_tb->aliquota_icms',".
                    "icms_reducao_bc='$produtos_tb->icms_reducao_bc',modalidade_deter_bc_icms_st='$produtos_tb->modalidade_deter_bc_icms_st',".
                    "icms_reducao_bc_st='$produtos_tb->icms_reducao_bc_st',perc_mva_icms_st='$produtos_tb->perc_mva_icms_st',".
                    "aliquota_icms_st='$produtos_tb->aliquota_icms_st',cst_ipi='$produtos_tb->ipi_cst',".
                    "calculo_ipi='$produtos_tb->calculo_ipi',cst_pis='$produtos_tb->cst_pis',".
                    "calculo_pis='$produtos_tb->calculo_pis',cst_cofins='$produtos_tb->cst_cofins',".
                    "calculo_cofins='$produtos_tb->calculo_cofins',aliquota_fcp='$produtos_tb->aliquota_fcp',".
                    "aliquota_fcp_st='$produtos_tb->aliquota_fcp_st',perc_dif='$produtos_tb->perc_dif' where codigo_interno=$produtos->codigo_interno";
        $result = pg_query($conexao, $query);
    }
    // Retornar uma resposta de sucesso com o código interno
    $response = array(
        "success" => true,
        "codigo_interno" => $produtos->codigo_interno,
        "message" => "Produto salvo com sucesso!"
    );
    echo json_encode($response);
    die();
}
if ($request == 'adicionar_item_grade') {
    $codigo_interno = $_POST['codigo_interno'];
    $codigo_gtin = $_POST['codigo_gtin'];
    $descricao = $_POST['descricao'];
    $variacao = $_POST['variacao'];
    $caracteristica = $_POST['caracteristica'];

    // Log dos dados recebidos
    error_log("DEBUG adicionar_item_grade: codigo_interno=$codigo_interno, codigo_gtin=$codigo_gtin, descricao=$descricao, variacao=$variacao, caracteristica=$caracteristica");

    // Validação do código GTIN
    if($codigo_gtin==0 or $codigo_gtin==''){
        error_log("ERRO adicionar_item_grade: codigo_gtin vazio ou zero");
        echo json_encode([
            'success' => false,
            'error' => 'Código GTIN é obrigatório'
        ]);
        die();
    }

    // Verificar se já existe item com mesmo GTIN
    $check_query = "SELECT codigo FROM produtos_gd WHERE codigo_gtin = '$codigo_gtin'";
    $check_result = pg_query($conexao, $check_query);

    if (!$check_result) {
        error_log("ERRO adicionar_item_grade: Erro ao verificar GTIN existente: " . pg_last_error($conexao));
        echo json_encode([
            'success' => false,
            'error' => 'Erro ao verificar GTIN: ' . pg_last_error($conexao)
        ]);
        die();
    }

    if (pg_num_rows($check_result) > 0) {
        error_log("ERRO adicionar_item_grade: GTIN $codigo_gtin já existe na grade");
        echo json_encode([
            'success' => false,
            'error' => 'GTIN já existe na grade'
        ]);
        die();
    }

    // Obter próximo código disponível
    $next_codigo_query = "SELECT COALESCE(MAX(codigo), 0) + 1 as next_codigo FROM produtos_gd";
    $next_codigo_result = pg_query($conexao, $next_codigo_query);

    if (!$next_codigo_result) {
        error_log("ERRO adicionar_item_grade: Erro ao obter próximo código: " . pg_last_error($conexao));
        echo json_encode([
            'success' => false,
            'error' => 'Erro ao obter próximo código: ' . pg_last_error($conexao)
        ]);
        die();
    }

    $next_codigo_row = pg_fetch_assoc($next_codigo_result);
    $next_codigo = $next_codigo_row['next_codigo'];

    error_log("DEBUG adicionar_item_grade: Próximo código: $next_codigo");

    // Construir query de inserção
    $query = "INSERT INTO produtos_gd (codigo, codigo_gtin, nome, caracteristica, variacao, codigo_interno)
              VALUES ($next_codigo, '$codigo_gtin', '$descricao', '$caracteristica', '$variacao', $codigo_interno)";

    error_log("DEBUG adicionar_item_grade: Executando query: $query");

    // Executar inserção
    $result = pg_query($conexao, $query);

    if (!$result) {
        $error = pg_last_error($conexao);
        error_log("ERRO adicionar_item_grade: Falha na inserção: $error");
        echo json_encode([
            'success' => false,
            'error' => 'Erro ao inserir na grade: ' . $error,
            'query' => $query
        ]);
        die();
    }

    // Verificar se alguma linha foi afetada
    $affected_rows = pg_affected_rows($result);
    error_log("DEBUG adicionar_item_grade: Linhas afetadas: $affected_rows");

    if ($affected_rows == 0) {
        error_log("AVISO adicionar_item_grade: Nenhuma linha foi inserida");
        echo json_encode([
            'success' => false,
            'error' => 'Nenhuma linha foi inserida na grade'
        ]);
        die();
    }

    // Sucesso
    error_log("SUCESSO adicionar_item_grade: Item adicionado com sucesso - GTIN: $codigo_gtin");
    echo json_encode([
        'success' => true,
        'message' => 'Item adicionado à grade com sucesso',
        'affected_rows' => $affected_rows
    ]);
    die();
}

if ($request == 'selecionar_itens_grade') {
    $response = "0";
    $codigo_interno = $_POST['codigo_interno'];

    $query = "select * from produtos_gd where codigo_interno=" . $codigo_interno." order by codigo" ;
    $result = pg_query($conexao, $query);

    $response = array();
    while ($row = pg_fetch_assoc($result)) {
        $response[] = array(
            "codigo" => $row['codigo'],
            "codigo_gtin" => $row['codigo_gtin'],
            "codigo_interno" => $row['codigo_interno'],
            "descricao" => $row['nome'],
            "variacao" => $row['variacao'],
            "caracteristica" => $row['caracteristica']
        );
    }
    echo json_encode($response);
    die();
}
if ($request == 'deleta_grade') {
    $response = "0";
    $codigo = $_POST['codigo'];
    $query = "delete from produtos_gd where codigo=" . $codigo;
    $result = pg_query($conexao, $query);
    echo json_encode("OK");
    die();
}
function gravar($texto){
    $arquivo = "logProdutos.txt";
    $fp = fopen($arquivo, "a+");
    fwrite($fp, $texto);
    fclose($fp);
}
if ($request == 'obterDadosProduto') {
    $codigo_interno = $_POST['codigo_interno'];

    $query = "SELECT p.codigo_interno, p.codigo_gtin, p.descricao, pb.descricao_detalhada,
              pb.preco_venda, po.peso, po.altura, po.largura, po.comprimento
              FROM produtos p
              LEFT JOIN produtos_ib pb ON p.codigo_interno = pb.codigo_interno
              LEFT JOIN produtos_ou po ON p.codigo_interno = po.codigo_interno
              WHERE p.codigo_interno = " . $codigo_interno;

    $result = pg_query($conexao, $query);
    $response = array();

    while ($row = pg_fetch_assoc($result)) {
        $response[] = array(
            "codigo_interno" => $row['codigo_interno'],
            "codigo_gtin" => $row['codigo_gtin'],
            "descricao" => $row['descricao'],
            "descricao_detalhada" => $row['descricao_detalhada'],
            "preco_venda" => $row['preco_venda'],
            "peso" => $row['peso'],
            "altura" => $row['altura'],
            "largura" => $row['largura'],
            "comprimento" => $row['comprimento']
        );
    }

    echo json_encode($response);
    die();
}

// Endpoint para sincronizar o status dos produtos com a Nuvemshop
if ($request == 'sincronizarStatusProdutos') {
    try {
        // Limpar qualquer saída anterior
        ob_clean();

        // Incluir arquivo de sincronização
        require_once 'produtos_ajax_sincronizacao.php';

        // Chamar a função de sincronização com novo fluxo
        $resultado = sincronizarStatusProdutos(array(), $conexao);

        // Definir header JSON
        header('Content-Type: application/json');

        // Retornar o resultado
        echo json_encode($resultado);
        exit;

    } catch (Exception $e) {
        // Definir header JSON
        header('Content-Type: application/json');

        // Retornar erro
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
        exit;
    }
}

// Endpoint para sincronizar status E estoque dos produtos com a Nuvemshop
if ($request == 'sincronizarStatusEEstoque') {
    try {
        // Limpar qualquer saída anterior
        ob_clean();

        // Incluir arquivo de sincronização
        require_once 'produtos_ajax_sincronizacao.php';

        // Chamar a função de sincronização completa
        $resultado = sincronizarStatusEEstoque($conexao);

        // Definir header JSON
        header('Content-Type: application/json');

        // Retornar o resultado
        echo json_encode($resultado);
        exit;

    } catch (Exception $e) {
        // Limpar qualquer saída anterior
        ob_clean();

        // Definir header JSON
        header('Content-Type: application/json');

        // Retornar erro
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        exit;
    } catch (Error $e) {
        // Limpar qualquer saída anterior
        ob_clean();

        // Definir header JSON
        header('Content-Type: application/json');

        // Retornar erro fatal
        echo json_encode([
            'success' => false,
            'error' => 'Erro fatal: ' . $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]);
        exit;
    }
}

// Endpoint para atualizar o status de e-commerce de um produto
if ($request == 'atualizarStatusEcommerce') {
    $codigo_interno = $_POST['codigo_interno'] ?? null;
    $codigo_gtin = $_POST['codigo_gtin'] ?? null;
    $status = $_POST['status'];

    // Log para debug
    error_log("DEBUG atualizarStatusEcommerce: codigo_interno=$codigo_interno, codigo_gtin=$codigo_gtin, status=$status");

    // Verificar se pelo menos um código foi informado
    if (!$codigo_interno && !$codigo_gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'Código interno ou GTIN deve ser informado'
        ]);
        die();
    }

    try {
        // Primeiro, verificar se o produto existe
        if ($codigo_gtin) {
            $check_query = "SELECT codigo_interno, codigo_gtin, descricao, status FROM produtos WHERE codigo_gtin = '$codigo_gtin'";
        } else {
            $check_query = "SELECT codigo_interno, codigo_gtin, descricao, status FROM produtos WHERE codigo_interno = $codigo_interno";
        }

        $check_result = pg_query($conexao, $check_query);

        if (!$check_result) {
            throw new Exception("Erro ao verificar produto: " . pg_last_error($conexao));
        }

        if (pg_num_rows($check_result) == 0) {
            $identificador = $codigo_gtin ? "GTIN $codigo_gtin" : "código interno $codigo_interno";
            error_log("DEBUG: Produto não encontrado com $identificador");
            throw new Exception("Produto não encontrado com $identificador");
        }

        $produto_atual = pg_fetch_assoc($check_result);
        error_log("DEBUG: Produto encontrado - GTIN: {$produto_atual['codigo_gtin']}, Status atual: '{$produto_atual['status']}', Novo status: '$status'");

        // Construir query baseada no parâmetro disponível
        if ($codigo_gtin) {
            // Usar GTIN para buscar o produto
            $query = "UPDATE produtos SET status = '$status' WHERE codigo_gtin = '$codigo_gtin'";
        } else {
            // Usar código interno (compatibilidade com código existente)
            $query = "UPDATE produtos SET status = '$status' WHERE codigo_interno = $codigo_interno";
        }

        error_log("DEBUG: Executando query: $query");
        $result = pg_query($conexao, $query);

        if (!$result) {
            throw new Exception("Erro ao atualizar status do produto: " . pg_last_error($conexao));
        }

        // Verificar se algum registro foi afetado
        $affected_rows = pg_affected_rows($result);
        error_log("DEBUG: Linhas afetadas: $affected_rows");

        if ($affected_rows == 0) {
            $identificador = $codigo_gtin ? "GTIN $codigo_gtin" : "código interno $codigo_interno";
            throw new Exception("Nenhum produto foi atualizado com $identificador");
        }

        // Verificar se a atualização realmente funcionou
        $verify_result = pg_query($conexao, $check_query);
        if ($verify_result) {
            $produto_verificado = pg_fetch_assoc($verify_result);
            error_log("DEBUG: Status após atualização: '{$produto_verificado['status']}'");
        }

        echo json_encode([
            'success' => true,
            'message' => 'Status atualizado com sucesso',
            'affected_rows' => $affected_rows,
            'debug' => [
                'produto_antes' => $produto_atual,
                'novo_status' => $status,
                'query' => $query
            ]
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}

// Endpoint para buscar um produto pelo código GTIN
if ($request == 'buscarProdutoPorGtin') {
    $codigo_gtin = $_POST['codigo_gtin'];

    if (!$codigo_gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'Código GTIN não informado'
        ]);
        die();
    }

    try {
        // Buscar o produto pelo código GTIN
        $query = "SELECT codigo_interno, codigo_gtin, descricao, status FROM produtos WHERE codigo_gtin = '$codigo_gtin'";
        $result = pg_query($conexao, $query);

        if (!$result) {
            throw new Exception("Erro ao buscar produto: " . pg_last_error($conexao));
        }

        $produtos = array();
        while ($row = pg_fetch_assoc($result)) {
            $produtos[] = $row;
        }

        echo json_encode($produtos);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}

// Endpoint para obter a quantidade de um produto pelo código GTIN
if ($request == 'obterQuantidadeProduto') {
    $codigo_gtin = $_POST['codigo_gtin'];

    if (!$codigo_gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'Código GTIN não informado'
        ]);
        die();
    }

    try {
        // Buscar quantidade, preço e dimensões do produto pelo código GTIN
        $query = "SELECT po.qtde, pb.preco_venda, po.peso, po.altura, po.largura, po.comprimento
                  FROM produtos p
                  INNER JOIN produtos_ou po ON p.codigo_interno = po.codigo_interno
                  INNER JOIN produtos_ib pb ON p.codigo_interno = pb.codigo_interno
                  WHERE p.codigo_gtin = '$codigo_gtin'";
        $result = pg_query($conexao, $query);

        if (!$result) {
            throw new Exception("Erro ao buscar dados do produto: " . pg_last_error($conexao));
        }

        if (pg_num_rows($result) > 0) {
            $row = pg_fetch_assoc($result);
            echo json_encode([
                'success' => true,
                'qtde' => $row['qtde'],
                'preco_venda' => $row['preco_venda'],
                'peso' => $row['peso'],
                'altura' => $row['altura'],
                'largura' => $row['largura'],
                'comprimento' => $row['comprimento']
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Produto não encontrado'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}

// Endpoint específico para buscar dimensões de uma variante
if ($request == 'obterDimensoesVariante') {
    $codigo_gtin = $_POST['codigo_gtin'];

    if (!$codigo_gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'Código GTIN não informado'
        ]);
        die();
    }

    try {
        // Buscar dimensões específicas da variante pelo código GTIN
        $query = "SELECT po.peso, po.altura, po.largura, po.comprimento,
                         pb.preco_venda, po.qtde, p.descricao
                  FROM produtos p
                  INNER JOIN produtos_ou po ON p.codigo_interno = po.codigo_interno
                  INNER JOIN produtos_ib pb ON p.codigo_interno = pb.codigo_interno
                  WHERE p.codigo_gtin = '$codigo_gtin'";
        $result = pg_query($conexao, $query);

        if (!$result) {
            throw new Exception("Erro ao buscar dimensões da variante: " . pg_last_error($conexao));
        }

        if (pg_num_rows($result) > 0) {
            $row = pg_fetch_assoc($result);
            echo json_encode([
                'success' => true,
                'codigo_gtin' => $codigo_gtin,
                'descricao' => $row['descricao'],
                'peso' => floatval($row['peso']) ?: 0,
                'altura' => floatval($row['altura']) ?: 0,
                'largura' => floatval($row['largura']) ?: 0,
                'comprimento' => floatval($row['comprimento']) ?: 0,
                'preco_venda' => $row['preco_venda'],
                'qtde' => intval($row['qtde']) ?: 0
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Variante não encontrada'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}

// Endpoint para buscar produto pai por GTIN (deve ser ENSVI)
if ($request == 'buscarProdutoPaiPorGtin') {
    $gtin = $_POST['gtin'];

    if (!$gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'GTIN não informado'
        ]);
        die();
    }

    try {
        // Buscar produto pai pelo GTIN com status ENSVI
        $query = "SELECT p.codigo_interno, p.codigo_gtin, p.descricao, p.status
                  FROM produtos p
                  WHERE p.codigo_gtin = '$gtin' AND p.status = 'ENSVI'";
        $result = pg_query($conexao, $query);

        if (!$result) {
            throw new Exception("Erro ao buscar produto pai: " . pg_last_error($conexao));
        }

        if (pg_num_rows($result) > 0) {
            $produto = pg_fetch_assoc($result);
            echo json_encode([
                'success' => true,
                'produto' => [
                    'codigo_interno' => $produto['codigo_interno'],
                    'codigo_gtin' => $produto['codigo_gtin'],
                    'descricao' => $produto['descricao'],
                    'status' => $produto['status']
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'error' => 'Produto pai não encontrado ou não possui status ENSVI'
            ]);
        }
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}

// Endpoint para atualizar dimensões de uma variante no banco de dados
if ($request == 'atualizarDimensoesVariante') {
    $codigo_gtin = $_POST['codigo_gtin'];
    $peso = $_POST['peso'];
    $altura = $_POST['altura'];
    $largura = $_POST['largura'];
    $comprimento = $_POST['comprimento'];

    if (!$codigo_gtin) {
        echo json_encode([
            'success' => false,
            'error' => 'Código GTIN não informado'
        ]);
        die();
    }

    try {
        // Primeiro, verificar se o produto existe
        $query_check = "SELECT codigo_interno FROM produtos WHERE codigo_gtin = '$codigo_gtin'";
        $result_check = pg_query($conexao, $query_check);

        if (!$result_check || pg_num_rows($result_check) == 0) {
            throw new Exception("Produto não encontrado com GTIN: $codigo_gtin");
        }

        $row = pg_fetch_assoc($result_check);
        $codigo_interno = $row['codigo_interno'];

        // Atualizar dimensões na tabela produtos_ou
        $query_update = "UPDATE produtos_ou SET
                         peso = " . floatval($peso) . ",
                         altura = " . floatval($altura) . ",
                         largura = " . floatval($largura) . ",
                         comprimento = " . floatval($comprimento) . "
                         WHERE codigo_interno = $codigo_interno";

        $result_update = pg_query($conexao, $query_update);

        if (!$result_update) {
            throw new Exception("Erro ao atualizar dimensões: " . pg_last_error($conexao));
        }

        // Verificar se alguma linha foi afetada
        $rows_affected = pg_affected_rows($result_update);

        if ($rows_affected > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Dimensões atualizadas com sucesso',
                'rows_affected' => $rows_affected
            ]);
        } else {
            // Se nenhuma linha foi afetada, pode ser que não existe registro em produtos_ou
            // Tentar inserir um novo registro
            $query_insert = "INSERT INTO produtos_ou (codigo_interno, peso, altura, largura, comprimento, qtde, qtde_min)
                            VALUES ($codigo_interno, " . floatval($peso) . ", " . floatval($altura) . ", " . floatval($largura) . ", " . floatval($comprimento) . ", 0, 0)";

            $result_insert = pg_query($conexao, $query_insert);

            if ($result_insert) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Dimensões inseridas com sucesso (novo registro)',
                    'action' => 'inserted'
                ]);
            } else {
                throw new Exception("Erro ao inserir dimensões: " . pg_last_error($conexao));
            }
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    die();
}
?>

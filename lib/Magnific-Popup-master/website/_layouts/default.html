<!DOCTYPE html>
<html lang="en" prefix="og: http://ogp.me/ns#">
  <head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">    

    <title>{{ page.title }}</title>
    
    <meta charset="utf-8"> 
    <meta name="description" content="{{page.description}}"/>
    <meta name="viewport" content="width = device-width, initial-scale = 1.0" />
    <meta name="author" content="Dmitry Semenov">
    <link rel="canonical" href="{{page.canonical_url}}" />

    <meta property="og:url" content="{{page.canonical_url}}">
    <meta property="og:title" content="{{page.title}}">
    <meta property="og:description" content="{{page.description}}">
    <meta property="og:image" content="http://dimsemenov.com/magnific-og-400x400.jpg">

    <link href="https://plus.google.com/102586375605352607718" rel="publisher" />

    {% if site.url == 'local' %}
      <link href="site-assets/site.css?v={{site.mfpversion}}" rel="stylesheet" />
      <link href="dist/magnific-popup.css?v={{site.mfpversion}}" rel="stylesheet" />
    {% else %}
      <link href="site-assets/all.min.css?v={{site.mfpversion}}" rel="stylesheet" />
    {% endif %}


    {% if page.title == 'Magnific Popup Documentation' %}
    <style type="text/css"> #main-wrapper{background: #FFF;} </style>
    {% endif %}

  </head>

  <body>
    
  <div id="main-wrapper">

    {{ content | markdown }}

    <div id="footer">
      Created by <a href="http://dimsemenov.com" rel="author">Dmitry Semenov</a>.{% if page.include_credit %} Photography in demo by <a href="http://www.squiver.com">Marsel Van Oosten</a>{% endif %}
    </div>

  </div>
  
  {{page.footer}}

  {% if page.addjs %}
    {% if site.url == 'local' %}
      <script>
        document.write('<script src=third-party-libs/' +
        ('__proto__' in {} ? 'zepto' : 'jquery') +
        '.js><\/script>')
      </script>
      <script src="dist/jquery.magnific-popup.js?v={{site.mfpversion}}"></script>
    {% else %}
      <script>
        document.write('<script src=third-party-libs/' +
        ('__proto__' in {} ? 'zepto' : 'jquery') +
        '.min.js><\/script>')
      </script>
      <script src="dist/jquery.magnific-popup.min.js?v={{site.mfpversion}}"></script>
    {% endif %}
  {% endif %}

  {% include analytics.html %}

  {% if page.buildtool %}
    {% include build-tool.html %}
  {% endif %}

  {% if site.url == 'local' %}
    <strong style="display:block; width:100%;text-align:center;">This page is just for tests and should not be used for production!</strong>
  {% endif %}
  </body>
</html>